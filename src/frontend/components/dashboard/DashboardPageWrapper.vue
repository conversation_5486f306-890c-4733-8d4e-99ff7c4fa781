<template>
  <UserLayout>
    <!-- Conditional Content Based on Route -->
    <div class="space-y-6">
      <!-- Settings Page (Vue Router) -->
      <router-view v-if="isSettingsPage" />

      <!-- Dashboard Pages (Traditional Layout) -->
      <template v-else>
        <!-- Mount points for existing Vue dashboard components -->
        <div id="vue-dashboard-content">
          <!-- This will be populated by the existing dashboard components -->
        </div>

        <!-- Vue 3 Dashboard App (for modals and components) -->
        <div id="dashboard-app" :data-active-tab="activeTab" :data-dashboard-data="dashboardDataJson"></div>
      </template>
    </div>
  </UserLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import UserLayout from '../../layouts/UserLayout.vue'

// Props from server-side data
interface Props {
  activeTab?: string
  dashboardData?: any
  user?: any
}

const props = withDefaults(defineProps<Props>(), {
  activeTab: 'domains',
  dashboardData: () => ({}),
  user: () => ({})
})

// Vue Router
const route = useRoute()

// Reactive state
const activeTab = ref(props.activeTab)
const dashboardDataJson = ref(JSON.stringify(props.dashboardData))

// Detect if we're on the settings page
const isSettingsPage = computed(() => {
  return route.name === 'settings'
})

// Initialize dashboard when component mounts
onMounted(() => {
  // Set global dashboard data for existing Vue components
  if (props.dashboardData) {
    (window as any).dashboardData = props.dashboardData
  }
  
  // The existing dashboard.ts will handle mounting Vue components
  // to the #vue-tabs and #vue-dashboard-content containers
  console.log('Dashboard page mounted with UserLayout, existing Vue components will initialize')
})
</script>

<style scoped>
/* Dashboard page specific styles */
</style>
