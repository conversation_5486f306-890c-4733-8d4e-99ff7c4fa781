<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

interface Tab {
  id: string
  name: string
  icon: string
  count?: number
  route: string
}

interface Props {
  counts: {
    domains: number
    aliases: number
    webhooks: number
  }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  createAction: [type: string]
}>()

// Router
const route = useRoute()
const router = useRouter()

// No longer need dropdown state - handled by DaisyUI

// Computed active tab from route
const activeTab = computed(() => {
  const routeName = route.name as string
  return routeName || 'domains'
})

// Computed
const tabs = computed<Tab[]>(() => [
  {
    id: 'domains',
    name: 'Domains',
    icon: 'M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9',
    count: props.counts.domains,
    route: '/domains'
  },
  {
    id: 'aliases',
    name: '<PERSON><PERSON>',
    icon: 'M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207',
    count: props.counts.aliases,
    route: '/aliases'
  },
  {
    id: 'webhooks',
    name: 'Webhooks',
    icon: 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1',
    count: props.counts.webhooks,
    route: '/webhooks'
  },
  {
    id: 'logs',
    name: 'Logs',
    icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
    route: '/logs'
  }
])

// Removed activeTabData - not used

const createButtonText = computed(() => {
  switch (activeTab.value) {
    case 'domains': return 'Create domain'
    case 'aliases': return 'Create alias'
    case 'webhooks': return 'Create webhook'
    case 'logs': return 'View logs'
    default: return 'Create'
  }
})

const createActions = [
  { id: 'create-domain', name: 'Create domain', icon: 'M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9' },
  { id: 'create-alias', name: 'Create alias', icon: 'M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207' },
  { id: 'create-webhook', name: 'Create webhook', icon: 'M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1' }
]

// Methods
const handleMainAction = () => {
  const actionMap: Record<string, string> = {
    'domains': 'create-domain',
    'aliases': 'create-alias',
    'webhooks': 'create-webhook',
    'logs': 'view-logs'
  }

  const action = actionMap[activeTab.value]
  if (action) {
    emit('createAction', action)
  }
}

const handleDropdownAction = (actionId: string) => {
  dropdownOpen.value = false
  emit('createAction', actionId)
}

const navigateToTab = (tab: Tab) => {
  // Use Vue Router for SPA navigation
  router.push(tab.route)
}

const closeDropdown = () => {
  dropdownOpen.value = false
}

// Close dropdown when clicking outside
onMounted(() => {
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement
    if (!target.closest('.dropdown-container')) {
      closeDropdown()
    }
  })
})
</script>

<template>
  <div class="bg-white">
    <div class="max-w-7xl mx-auto px-6 lg:px-8">
      <div class="flex justify-between items-center py-4">
        <!-- Tab Navigation using DaisyUI tabs -->
        <div class="tabs">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="navigateToTab(tab)"
            :class="[
              'tab px-4 py-2 text-sm font-medium transition-colors rounded-lg',
              tab.id === activeTab
                ? 'bg-blue-50 text-blue-600'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            ]"
          >
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="tab.icon" />
              </svg>
              <span>{{ tab.name }}</span>
              <span 
                v-if="tab.count !== undefined"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
              >
                {{ tab.count }}
              </span>
            </div>
          </button>
        </div>

        <!-- Split Button: Main Action + Dropdown (hide on logs tab) -->
        <div v-if="activeTab !== 'logs'" class="flex">
          <!-- Main Create Button (Dynamic) -->
          <button
            type="button"
            @click="handleMainAction"
            class="inline-flex items-center px-3 sm:px-4 py-2 border border-transparent text-sm font-medium rounded-l-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <svg class="-ml-1 mr-1 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span class="hidden sm:inline">{{ createButtonText }}</span>
            <span class="sm:hidden">Create</span>
          </button>

          <!-- DaisyUI Dropdown -->
          <div class="dropdown dropdown-end">
            <!-- Dropdown Toggle Button -->
            <div
              tabindex="0"
              role="button"
              class="inline-flex items-center px-2 py-2 border-l border-blue-500 text-sm font-medium rounded-r-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              <svg
                class="h-4 w-4 sm:h-5 sm:w-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>

            <!-- Dropdown menu -->
            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-48 p-2 shadow">
              <li v-for="action in createActions" :key="action.id">
                <button
                  type="button"
                  @click="handleDropdownAction(action.id)"
                  class="flex items-center gap-2 w-full text-left"
                >
                  <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="action.icon" />
                  </svg>
                  <span>{{ action.name }}</span>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <!-- Subtle separator line -->
    <div class="border-b border-gray-100"></div>
  </div>
</template>
