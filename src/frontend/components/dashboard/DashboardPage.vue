<template>
  <UserLayout>
    <!-- Vue Tab Navigation -->
    <div id="vue-tabs"></div>

    <!-- Main Dashboard Content -->
    <main class="px-6 mx-auto max-w-7xl lg:px-8">
      <!-- Vue Dashboard Container -->
      <div id="vue-dashboard-content">
        <!-- Vue Router will render the appropriate dashboard view here -->
        <!-- This div serves as the mounting point for Vue dashboard components -->
      </div>
    </main>

    <!-- Vue 3 Dashboard App (for modals and components) -->
    <div id="dashboard-app" :data-active-tab="activeTab" :data-dashboard-data="dashboardDataJson"></div>
  </UserLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import UserLayout from '../../layouts/UserLayout.vue'

// Props from server-side data
interface Props {
  activeTab?: string
  dashboardData?: any
}

const props = withDefaults(defineProps<Props>(), {
  activeTab: 'domains',
  dashboardData: () => ({})
})

// Reactive state
const activeTab = ref(props.activeTab)
const dashboardDataJson = ref(JSON.stringify(props.dashboardData))

// Initialize dashboard when component mounts
onMounted(() => {
  // Set global dashboard data for existing Vue components
  if (props.dashboardData) {
    (window as any).dashboardData = props.dashboardData
  }
  
  // Initialize existing Vue dashboard components
  // These will mount to the #vue-tabs and #vue-dashboard-content divs
  initializeDashboardComponents()
})

// Function to initialize existing dashboard Vue components
const initializeDashboardComponents = () => {
  // The existing dashboard.ts will handle mounting Vue components
  // to the #vue-tabs and #vue-dashboard-content containers
  console.log('Dashboard page mounted, existing Vue components will initialize')
}
</script>

<style scoped>
/* Dashboard page specific styles */
</style>
