import { createApp } from 'vue'
import { createPinia } from 'pinia'
import DashboardPageWrapper from './components/dashboard/DashboardPageWrapper.vue'
import router from './router'
import './style.css'

// Get dashboard data from server
const dashboardData = (window as any).dashboardData || {}
const activeTab = new URLSearchParams(window.location.search).get('tab') || 'domains'

// Create and mount the dashboard page app with UserLayout
const app = createApp(DashboardPageWrapper, {
  activeTab,
  dashboardData,
  user: dashboardData.user || {}
})

// Add Vue Router and Pinia
const pinia = createPinia()
app.use(pinia)
app.use(router)

app.mount('#dashboard-vue-app')

// Also load the existing dashboard components for actual functionality
// This ensures we have both the Vue UserLayout AND the working dashboard content
setTimeout(() => {
  import('./dashboard.ts').then(() => {
    console.log('Vue UserLayout + existing dashboard components loaded successfully')
  }).catch(error => {
    console.error('Failed to load dashboard components:', error)
  })
}, 200)
