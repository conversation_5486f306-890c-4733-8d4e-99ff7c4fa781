import { createApp } from 'vue'
import { createPinia } from 'pinia'
import DashboardApp from './DashboardApp.vue'
import router from './router'
import './style.css'

// Get dashboard data from server
const dashboardData = (window as any).dashboardData || {}

// Create and mount the consolidated dashboard app
const app = createApp(DashboardApp)

// Add Vue Router and Pinia
const pinia = createPinia()
app.use(pinia)
app.use(router)

// Mount the single Vue app
app.mount('#dashboard-vue-app')

console.log('Consolidated Vue dashboard app mounted successfully')
