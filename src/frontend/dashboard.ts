import { createApp } from 'vue'
import { createPinia } from 'pinia'
import DashboardApp from './DashboardApp.vue'
import UserHeader from './components/layout/UserHeader.vue'
import router from './router'
import './style.css'

// Create main dashboard app with router and pinia
const dashboardApp = createApp(DashboardApp)
const pinia = createPinia()
dashboardApp.use(pinia)
dashboardApp.use(router)

// Mount when DOM is ready
function mountDashboard() {
  // Mount Vue header
  const headerContainer = document.getElementById('vue-user-header')
  if (headerContainer) {
    try {
      const headerApp = createApp(UserHeader)
      headerApp.mount('#vue-user-header')
    } catch (error) {
      console.error('Failed to mount Vue header:', error)
    }
  }

  // Mount main dashboard app
  const dashboardContainer = document.getElementById('dashboard-app')
  if (dashboardContainer) {
    try {
      dashboardApp.mount('#dashboard-app')

      // Hide any Alpine.js fallbacks
      const alpineTabsContainer = document.getElementById('alpine-tabs')
      if (alpineTabsContainer) {
        alpineTabsContainer.style.display = 'none'
      }
    } catch (error) {
      console.error('Failed to mount Vue dashboard:', error)
    }
  }
}

// Check if DOM is already loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', mountDashboard)
} else {
  // DOM is already loaded, mount immediately
  mountDashboard()
}