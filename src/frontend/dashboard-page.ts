import { createApp } from 'vue'
import DashboardPage from './components/dashboard/DashboardPage.vue'
import './style.css'

// Get dashboard data from server
const dashboardData = (window as any).dashboardData || {}
const activeTab = new URLSearchParams(window.location.search).get('tab') || 'domains'

// Create and mount the dashboard page app
const app = createApp(DashboardPage, {
  activeTab,
  dashboardData
})

app.mount('#dashboard-page-app')

// Also initialize the existing dashboard components
// Import and run the existing dashboard initialization
import('./dashboard.ts').then(() => {
  console.log('Existing dashboard components initialized')
})
